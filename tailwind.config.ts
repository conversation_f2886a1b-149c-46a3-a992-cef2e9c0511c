import type { Config } from "tailwindcss"

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#52C3C5", // main primary
          light: "#66D9FF",  // lighter shade
          dark: "#0080A0",   // darker shade
        },
        secondary: {
          DEFAULT: "#25cf52ff",
          light: "#25cf52ff",
          dark: "#25cf52ff",
        },
        neutral: {
          100: "#f5f5f5",
          200: "#e5e5e5",
          900: "#111111",
        }
      },
    },
  },
  plugins: [],
}

export default config
