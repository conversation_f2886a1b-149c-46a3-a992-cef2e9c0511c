import MainLayout from "@/layouts/main-layout";
import React from "react";
import { dummyMembers } from "@/lib/dummy-data";
import { DataCard } from "@/components/constant/data-card";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import { getStatusColor, getRoleColor } from "@/lib/member-colors";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@radix-ui/react-select";

const badges = [
  {
    label: "Active",
    dotColor: "#22c55e", // green-500

    className: "bg-green-100 text-green-800",
  },
  {
    label: "Editor",
    className: "bg-blue-100 text-blue-800",
    dotColor: "#22c55e", // green-500
  },
];

const buttons = [
  {
    label: "View",
    icon: Eye,
    className: "bg-[#D7E1E4] text-black hover:bg-[#c6d7db]",
  },
  {
    label: "Edit",
    icon: Edit,
    className: "bg-[#D7E1E4] text-black hover:bg-[#c6d7db]",
  },
  {
    label: "Deactivate",
    icon: Trash2,
    className: "bg-[#D1E4EB]/62 text-[#CD0035] hover:bg-[#bcd6e0]",
  },
];

export default function page() {
  return (
    <MainLayout>
      <div className="flex justify-between items-center ">
        <h1 className="text-2xl font-bold">Scholar Management</h1>
      </div>
      <div className="flex justify-between items-center mb-4 text-lg">
        Manage and monitor all platform users
      </div>
      <div className="flex justify-between items-center mb-6 w-full">
        {/* Left: Search */}
        <div className="w-1/3">
          <Input
            placeholder="Search members..."
            className="w-full bg-gray-800 text-white placeholder-gray-400 border border-gray-700 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 transition-all duration-200"
          />
        </div>

        {/* Right: Filters */}
        <div className="flex gap-4">
          {/* Status Filter */}
          <Select>
            <SelectTrigger className="w-[180px] bg-gray-800 text-white placeholder-gray-400 border border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:ring-2 focus:ring-blue-500">
              <SelectValue placeholder="Filter by Status" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 text-white rounded-lg shadow-lg animate-fadeIn">
              <SelectItem value="active" className="hover:bg-gray-700">
                Active
              </SelectItem>
              <SelectItem value="inactive" className="hover:bg-gray-700">
                Inactive
              </SelectItem>
              <SelectItem value="pending" className="hover:bg-gray-700">
                Pending
              </SelectItem>
            </SelectContent>
          </Select>

          {/* Plan Filter */}
          <Select>
            <SelectTrigger className="w-[180px] bg-gray-800 text-white placeholder-gray-400 border border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 focus:ring-2 focus:ring-blue-500">
              <SelectValue placeholder="Filter by Plan" />
            </SelectTrigger>
            <SelectContent className="bg-gray-800 text-white rounded-lg shadow-lg animate-fadeIn">
              <SelectItem value="free" className="hover:bg-gray-700">
                Free
              </SelectItem>
              <SelectItem value="basic" className="hover:bg-gray-700">
                Basic
              </SelectItem>
              <SelectItem value="pro" className="hover:bg-gray-700">
                Pro
              </SelectItem>
              <SelectItem value="enterprise" className="hover:bg-gray-700">
                Enterprise
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4 full-width-container">
        {dummyMembers.map((member) => {
          // Generate JSX buttons dynamically
          const actions = buttons.map((btn, idx) => {
            const Icon = btn.icon;
            return (
              <Button key={idx} size="sm" className={btn.className}>
                <Icon className="h-4 w-4 mr-1" /> {btn.label}
              </Button>
            );
          });

          return (
            <DataCard
              key={member.id}
              id={member.id}
              avatar={`https://images.ctfassets.net/h6goo9gw1hh6/2sNZtFAWOdP1lmQ33VwRN3/24e953b920a9cd0ff2e1d587742a2472/1-intro-photo-final.jpg?w=1200&h=992&fl=progressive&q=70&fm=jpg`}
              fallback={member.name
                .split(" ")
                .map((n) => n[0])
                .join("")}
              title={member.name}
              subtitle={member.email}
              badges={badges}
              actions={actions} // <-- now this is ReactNode
            />
          );
        })}
      </div>
    </MainLayout>
  );
}
