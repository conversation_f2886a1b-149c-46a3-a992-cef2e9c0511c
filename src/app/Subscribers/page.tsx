import MainLayout from "@/layouts/main-layout";
import React from "react";
import { dummyMembers } from "@/lib/dummy-data";
import { DataCard } from "@/components/constant/data-card";
import { Button } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import { getStatusColor, getRoleColor } from "@/lib/member-colors";

export default function page() {
  return (
    <MainLayout>
      <div className="space-y-4">
        {dummyMembers.map((member) => (
          <DataCard
            key={member.id}
            id={member.id}
            avatar={`/abstract-geometric-shapes.png?height=40&width=40&query=${member.name}`}
            fallback={member.name
              .split(" ")
              .map((n) => n[0])
              .join("")}
            title={member.name}
            subtitle={member.email}
            badges={[
              {
                label: member.status,
                className: getStatusColor(member.status),
              },
              { label: member.role, className: getRoleColor(member.role) },
              ...(member.role === "content" && member.createdContent
                ? [
                    {
                      label: `${member.createdContent} contents`,
                      className: "border",
                    },
                  ]
                : []),
            ]}
            meta={[
              {
                label: "Joined",
                value: new Date(member.joinDate).toLocaleDateString(),
              },
              {
                label: "Last Login",
                value: new Date(member.lastLogin).toLocaleDateString(),
              },
            ]}
            actions={
              <>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-1" /> View
                </Button>
                <Button variant="outline" size="sm">
                  <Edit className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:text-destructive bg-transparent"
                >
                  <Trash2 className="h-4 w-4 mr-1" /> Deactivate
                </Button>
              </>
            }
          />
        ))}
      </div>
    </MainLayout>
  );
}
