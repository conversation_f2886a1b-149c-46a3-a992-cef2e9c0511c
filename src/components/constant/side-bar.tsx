"use client";

import type React from "react";
import { useState } from "react";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ChevronLeft,
  ChevronRight,
  Home,
  Settings,
  User,
  FileText,
  BarChart3,
} from "lucide-react";

interface SidebarProps {
  children: React.ReactNode;
}

export function Sidebar({ children }: SidebarProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const pathname = usePathname();
  console.log(pathname);

  const toggleSidebar = () => setIsExpanded(!isExpanded);

  const menuItems = [
    { icon: Home, label: "Dashboard", href: "/dashboard" },
    { icon: FileText, label: "Subscribers", href: "/subscribers" },
    { icon: BarChart3, label: "LE Users", href: "/leUsers" },
    { icon: User, label: "Content", href: "/content" },
    { icon: Settings, label: "Finance", href: "/finance" },
    { icon: Settings, label: "Sponsor & ads", href: "/sponsorAds" },
    { icon: Settings, label: "Results", href: "/results" },
    { icon: Settings, label: "Audit Log", href: "/auditLog" },
    { icon: Settings, label: "Master Data", href: "/masterData" },
  ];

  return (
    <div className="flex h-screen bg-black text-white">
      {/* Sidebar */}
      <div
        className={cn(
          "relative flex flex-col bg-[#080F17] border-r border-zinc-800 transition-all duration-300 ease-in-out mt-14",
          isExpanded ? "w-64" : "w-16"
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-zinc-800">
          {isExpanded && (
            <h2 className="text-lg font-semibold text-white">Menu</h2>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="text-gray-400 hover:bg-zinc-800 hover:text-white"
          >
            {isExpanded ? (
              <ChevronLeft className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;

            // ✅ Check if current route matches
            const isActive =
              item.href === "/"
                ? pathname === "/"
                : pathname.startsWith(item.href);

            return (
              <a
                key={item.href}
                href={item.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-colors",
                  !isExpanded && "justify-center",
                  isActive
                    ? "bg-[#52C3C5]/6 text-[#52C3C5] font-semibold"
                    : "text-gray-400 hover:bg-zinc-800 hover:text-white"
                )}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {isExpanded && (
                  <span className="text-sm font-medium">{item.label}</span>
                )}
              </a>
            );
          })}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden bg-black">
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
    </div>
  );
}
