"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import type { ReactNode } from "react";

interface DataCardProps {
  id: string | number;
  avatar?: string;
  fallback?: string;
  title: string;
  subtitle?: string;
  badges?: { label: string; className?: string }[];
  meta?: { label: string; value: string }[];
  actions?: ReactNode; // Buttons passed from parent
}

export function DataCard({
  id,
  avatar,
  fallback,
  title,
  subtitle,
  badges = [],
  meta = [],
  actions,
}: DataCardProps) {
  return (
    <Card key={id} className="w-full hover:shadow-md transition-shadow">
      <CardContent className="p-4 w-full">
        {/* Top Section */}
        <div className="flex items-center justify-between w-full">
          {/* Avatar + Info */}
          <div className="flex items-center gap-4 w-full">
            <Avatar>
              {avatar ? (
                <AvatarImage src={avatar} />
              ) : (
                <AvatarFallback>{fallback}</AvatarFallback>
              )}
            </Avatar>
            <div className="flex-1">
              <h3 className="font-semibold text-foreground">{title}</h3>
              {subtitle && (
                <p className="text-sm text-muted-foreground">{subtitle}</p>
              )}

              {/* Badges */}
              {badges.length > 0 && (
                <div className="flex gap-2 mt-2 flex-wrap">
                  {badges.map((badge, i) => (
                    <Badge key={i} className={cn(badge.className)}>
                      {badge.label}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center gap-2">{actions}</div>
        </div>

        {/* Metadata (Joined, Last Login, etc.) */}
        {meta.length > 0 && (
          <div className="mt-4 grid grid-cols-2 gap-4 text-sm text-muted-foreground w-full">
            {meta.map((item, i) => (
              <div key={i}>
                <span className="font-medium">{item.label}: </span>
                {item.value}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
