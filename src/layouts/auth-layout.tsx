import React, { ReactNode } from "react";

interface AuthLayoutProps {
  children: ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="flex flex-col h-screen bg-black text-white">
      <header className="w-full p-4 shadow-md border-b border-gray-700">
        <h1 className="text-lg font-semibold">Login Screen</h1>
      </header>

      <main className="flex flex-1 justify-center items-center">
        {children}
      </main>
    </div>
  );
}
