import { Sidebar } from "@/components/constant/side-bar";
import Header from "@/components/constant/header";
import React, { ReactNode } from "react";

interface MainLayoutProps {
  children: ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex h-screen bg-black text-white">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header title="Dashboard" />

        {/* Page Content */}
        <main className="flex-1 mt-14 p-6 overflow-auto main-content">
          <div className="full-width-container">{children}</div>
        </main>
      </div>
    </div>
  );
}
